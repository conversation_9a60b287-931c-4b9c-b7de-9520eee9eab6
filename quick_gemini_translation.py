import os
import google.generativeai as genai
import keyboard
import pyperclip
import time
import ctypes
import winsound

# Load Windows user32 library
user32 = ctypes.windll.user32

def remove_empty_lines(text): 
  # Split the string into lines, filter out empty lines, and join back
  return '\n'.join(line for line in text.split('\n') if line.strip())

def translate_clipboard():
  # Read text from clipboard
  original_text = pyperclip.paste()


  if not original_text:
      print("No text found in clipboard.")
      return

  print("Translating...")
  print(original_text)
  original_text = remove_empty_lines(original_text)
  print(original_text)

  try:
      genai.configure(api_key="AIzaSyB1Q8nnZwfHrpDMdHblHul4eod29QxNjWM")
      model = genai.GenerativeModel('gemini-2.0-flash')

      prompt = f""""
          You are a professional english-arabic translator, you must translate the text given to you accuretly matching the meaning of the original text,
          while also trying to change the style to match the target langauge style and tone,
          if the text is in arabic, translate to english, if it is in english, translate to arabic. give the text only without extra explanation,
          Translate arabic numerals to english ones, but DO NOT add commas if they don't exist in the source number. 
           translate the following text: \n
           
           {original_text}
           """

      response = model.generate_content(prompt)

      output = response.text

      print(output)
    
      translated_text = output

      # Copy translated text to clipboard 
      pyperclip.copy(translated_text)
      print("Translation complete and copied to clipboard.")
      
      # Play a system sound
      winsound.PlaySound(r"C:\Users\<USER>\Downloads\Music\cute_notification.mp3", winsound.SND_ALIAS)
            
  except Exception as e:
      print(f"An error occurred: {str(e)}")

def main():
  print("Press win + alt to translate text from clipboard.")
  keyboard.add_hotkey('win+alt', translate_clipboard)
  keyboard.wait()

if __name__ == "__main__":
  main()