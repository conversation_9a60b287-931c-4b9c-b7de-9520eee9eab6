import os
import google.generativeai as genai
import keyboard
import pyperclip
import winsound
from pathlib import Path


def clean_text(text):
    """Remove empty lines and extra whitespace from text."""
    return '\n'.join(line.strip() for line in text.split('\n') if line.strip())


def get_api_key():
    """Get API key from environment variable."""
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        raise ValueError("GEMINI_API_KEY environment variable not set. Please set it with your API key.")
    return api_key


def play_notification_sound():
    """Play a notification sound if available."""
    try:
        # Try to play a system sound first
        winsound.PlaySound("SystemAsterisk", winsound.SND_ALIAS | winsound.SND_ASYNC)
    except:
        # If system sound fails, try to find a custom sound file
        sound_paths = [
            Path.home() / "Downloads" / "Music" / "cute_notification.mp3",
            Path.cwd() / "notification.mp3",
            Path.cwd() / "notification.wav"
        ]

        for sound_path in sound_paths:
            if sound_path.exists():
                try:
                    winsound.PlaySound(str(sound_path), winsound.SND_FILENAME | winsound.SND_ASYNC)
                    return
                except:
                    continue


def translate_text(text, api_key):
    """Translate text using Gemini AI."""
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-2.0-flash')

    prompt = f"""You are a professional English-Arabic translator. Translate the text accurately while matching the target language's style and tone.

Rules:
- If text is in Arabic, translate to English
- If text is in English, translate to Arabic
- Provide only the translation without explanations
- Convert Arabic numerals to English numerals
- Do NOT add commas if they don't exist in the source number

Text to translate:
{text}"""

    response = model.generate_content(prompt)
    return response.text.strip()


def translate_clipboard():
    """Main translation function that processes clipboard content."""
    try:
        # Get clipboard content
        original_text = pyperclip.paste().strip()

        if not original_text:
            print("No text found in clipboard.")
            return

        print(f"Translating: {original_text[:50]}{'...' if len(original_text) > 50 else ''}")

        # Clean the text
        cleaned_text = clean_text(original_text)

        # Get API key
        api_key = get_api_key()

        # Translate
        translated_text = translate_text(cleaned_text, api_key)

        # Copy to clipboard
        pyperclip.copy(translated_text)

        print(f"Translation: {translated_text}")
        print("Translation copied to clipboard!")

        # Play notification sound
        play_notification_sound()

    except ValueError as e:
        print(f"Configuration error: {e}")
    except Exception as e:
        print(f"Translation error: {e}")


def main():
    """Main function to set up hotkey and wait for user input."""
    print("Quick Translation Tool")
    print("Press Win + Alt to translate clipboard text")
    print("Press Ctrl + C to exit")

    try:
        # Verify API key is available
        get_api_key()
        print("API key loaded successfully")
    except ValueError as e:
        print(f"Setup error: {e}")
        return

    keyboard.add_hotkey('win+alt', translate_clipboard)

    try:
        keyboard.wait()
    except KeyboardInterrupt:
        print("\nExiting...")


if __name__ == "__main__":
    main()