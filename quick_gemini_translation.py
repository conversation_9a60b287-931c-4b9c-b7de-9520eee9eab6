import os
import google.generativeai as genai
import keyboard
import pyperclip
import winsound


def play_notification_sound():
    """Play cute_notification.mp3 first, then system sound as fallback."""
    try:
        winsound.PlaySound("cute_notification.mp3", winsound.SND_FILENAME | winsound.SND_ASYNC)
    except:
        winsound.PlaySound("SystemAsterisk", winsound.SND_ALIAS | winsound.SND_ASYNC)


def translate_clipboard():
    """Translate clipboard text using Gemini AI."""
    try:
        # Get text from clipboard
        text = pyperclip.paste().strip()
        if not text:
            print("No text in clipboard.")
            return

        print(f"Translating: {text[:50]}...")

        # Configure Gemini
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("Error: Set GEMINI_API_KEY environment variable")
            return

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.0-flash')

        # Translate
        prompt = f"""Translate this text. If Arabic→English, if English→Arabic. Only return the translation:
{text}"""

        response = model.generate_content(prompt)
        translation = response.text.strip()

        # Copy to clipboard and notify
        pyperclip.copy(translation)
        print(f"Translation: {translation}")
        print("Copied to clipboard!")
        play_notification_sound()

    except Exception as e:
        print(f"Error: {e}")


def main():
    print("Quick Translation Tool - Press Win+Alt to translate")
    keyboard.add_hotkey('win+alt', translate_clipboard)
    keyboard.wait()


if __name__ == "__main__":
    main()

    